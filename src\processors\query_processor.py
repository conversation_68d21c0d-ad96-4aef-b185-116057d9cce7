import logging
import time
import uuid
import copy
import math
from typing import Optional, List, Tuple, Dict, Any, Union

from src.core.connection import ClickHouseConnection
from src.queries.query_builder import ClickHouseQuery
from src.core.result_store import ResultStore
from src.models.kpi import KPIType
from src.utils.job_api_client import set_job_progress_message
from src.models.axis import Period
from src.core.exceptions import QueryError


class QueryProcessor:
    """Class to handle query processing for KPI analysis."""

    def __init__(
        self,
        connection: ClickHouseConnection,
        query_builder: ClickHouseQuery,
        result_store: ResultStore,
    ):
        """
        Initialize QueryProcessor.

        Args:
            connection: ClickHouse database connection
            query_builder: Query builder instance
            result_store: Result store instance

        Raises:
            ValueError: If any required parameter is None
        """
        # Validate required parameters
        if not connection:
            raise ValueError("ClickHouse connection is required")
        if not query_builder:
            raise ValueError("Query builder is required")
        if not result_store:
            raise ValueError("Result store is required")

        # Store dependencies
        self.connection = connection
        self.query_builder = query_builder
        self.result_store = result_store

        # Initialize logger and state
        self.logger = logging.getLogger(self.__class__.__name__)
        self.temp_tables: List[str] = []

    def _create_temp_table(
        self, table_name: str, query: str, query_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Create a temporary table and track it.

        Args:
            table_name: Name of the temporary table to create
            query: SQL query to populate the table
            query_id: Optional custom query ID for tracking

        Returns:
            Table name if successful, None if failed
        """
        self.logger.info(f"Creating temporary table '{table_name}'")
        settings = {"query_id": query_id} if query_id else None

        try:
            result = self.connection.create_temp_table(
                query, table_name, "Memory", None, settings
            )

            # Check if the result is a tuple indicating an error
            if isinstance(result, tuple) and len(result) == 2 and result[0] == "ERROR":
                error_msg = f"Error creating temp table {table_name}: {result[1]}"
                self.logger.error(error_msg)
                return None

            # Add to tracking list
            self.temp_tables.append(table_name)
            self.logger.info(f"Temporary table '{table_name}' created successfully")
            return table_name

        except Exception as e:
            self.logger.error(f"Exception creating temp table {table_name}: {e}")
            return None

    def _drop_temp_table(self, table_name: str) -> bool:
        """
        Drop a specific temporary table.

        Args:
            table_name: Name of the temporary table to drop

        Returns:
            True if successful, False otherwise
        """
        if table_name not in self.temp_tables:
            self.logger.warning(
                f"Temporary table '{table_name}' not found in tracking list"
            )
            return False

        try:
            self.logger.info(f"Dropping temporary table '{table_name}'")
            result = self.connection.drop_temp_table(table_name)

            if result:
                self.temp_tables.remove(table_name)
                self.logger.info(f"Temporary table '{table_name}' dropped successfully")
                return True
            else:
                self.logger.warning(f"Failed to drop temporary table '{table_name}'")
                return False

        except QueryError as e:
            # Log the error but continue
            self.logger.warning(
                f"ClickHouse error dropping temporary table '{table_name}': {e}"
            )
            if hasattr(e, "error_code"):
                self.logger.warning(
                    f"Error code: {e.error_code}, type: {getattr(e, 'error_type', 'unknown')}"
                )
            return False
        except Exception as e:
            self.logger.warning(f"Failed to drop temporary table '{table_name}': {e}")
            return False

    def _cleanup_temp_tables(self, temp_tables: Optional[List[str]] = None) -> None:
        """
        Clean up temporary tables.

        Args:
            temp_tables: List of temporary table names to drop. If None, all tables in self.temp_tables will be dropped.
        """
        # Determine which tables to drop
        tables_to_drop = temp_tables if temp_tables is not None else self.temp_tables[:]

        # Skip if no tables to drop
        if not tables_to_drop:
            self.logger.debug("No temporary tables to clean up")
            return

        self.logger.info(f"Cleaning up {len(tables_to_drop)} temporary tables")

        # Drop each table
        for table in tables_to_drop[:]:
            self._drop_temp_table(table)

        # Log remaining tables
        if self.temp_tables:
            self.logger.debug(
                f"Remaining temporary tables: {', '.join(self.temp_tables)}"
            )

    def _get_axis_positions(self, axis_key: str) -> int:
        """Helper to get the number of positions for a given axis key from query_builder."""
        if not self.query_builder or axis_key not in self.query_builder.axes:
            self.logger.warning(
                f"_get_axis_positions: Query builder not set or axis key '{axis_key}' not found."
            )
            return 0

        axis_data = self.query_builder.axes[axis_key]

        if not axis_data:
            self.logger.warning(
                f"_get_axis_positions: No axis_data for key '{axis_key}'."
            )
            return 0

        if not axis_data.type:  # Ensure axis_data.type is not None
            self.logger.warning(
                f"_get_axis_positions: Axis type is None for key '{axis_key}'."
            )
            return 0

        if axis_data.type == "axsh":
            # For "axsh", number of DDL queries often corresponds to positions
            if axis_data.ddl and "queries" in axis_data.ddl:
                return len(axis_data.ddl.get("queries", []))
            self.logger.debug(
                f"_get_axis_positions: axsh '{axis_key}' has no DDL queries, returning 0 positions."
            )
            return 0
        elif axis_data.labels:
            # For other types like "axft", "axcl", count labels
            return len(axis_data.labels)

        self.logger.debug(
            f"_get_axis_positions: Axis '{axis_key}' (type: {axis_data.type}) has no DDL queries or labels, returning 0 positions."
        )
        return 0

    def process_query(
        self,
        period: Union[Period, Tuple[str, str, str]],
        kpi_type: KPIType = KPIType.STANDARD_KPI,
        job_id: str = "unknown",
        analysis_name: str = "kpi_analysis",
        id_panel: int = 1,
        combined_result_id: Optional[str] = None,
        username: str = "",
        su_fact_name: Optional[str] = None,
    ) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        Process a KPI query and store the results directly in ClickHouse.

        Args:
            period: Period model or Tuple of (period_name, start_date, end_date)
            kpi_type: Type of KPI to process (STANDARD_KPI or CATMAN_KPI)
            job_id: Job ID for tracking
            analysis_name: Analysis name
            id_panel: Panel ID
            combined_result_id: Optional ID for combining multiple periods into one table
            username: Username for the job
            su_fact_name: Name of the SU fact used in the query

        Returns:
            Tuple of (Result ID if successful or None, Error info if failed or None)
        """
        # Start tracking total job execution time
        job_start_time = time.time()

        # Track all query steps and query IDs
        query_steps = []
        query_ids = []

        # Generate custom query ID prefix based on job_id and timestamp
        query_id_prefix = f"{job_id}_{int(time.time())}_"

        # Initialize period_name and period_tuple with defaults to ensure they are bound in except block
        # These will be updated in the try block if period processing is successful.
        period_name: str = "Unknown Period"
        period_tuple: Tuple[str, str, str] = (
            "unknown_period",
            "1900-01-01",
            "1900-01-01",
        )
        period_model_for_builder: Optional[Period] = None

        try:
            # Convert Period model to tuple if needed and set it in the query builder
            if isinstance(period, Period):
                period_name = period.label
                period_start = period.date_start
                period_end = period.date_end
                period_tuple = (
                    period_name,
                    period_start,
                    period_end,
                )  # Overwrite default
                period_model_for_builder = period
                self.logger.info(
                    f"Processing period from Period model: {period_name} ({period_start} to {period_end})"
                )
            elif isinstance(period, tuple) and len(period) == 3:
                period_name = period[0]  # Overwrite default
                period_start = period[1]
                period_end = period[2]
                period_tuple = period  # Overwrite default
                period_model_for_builder = Period(
                    label=period_name, date_start=period_start, date_end=period_end
                )
                self.logger.info(
                    f"Processing period from tuple: {period_name} ({period_start} to {period_end})"
                )
            else:
                # Validate period tuple - error will be caught by the main except block
                # period_name and period_tuple will retain their default "Unknown" values
                error_msg = f"Invalid period format: {period}. Expected Period model or (name, start_date, end_date) tuple"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # Set the Period model in the query builder
            if period_model_for_builder:
                self.query_builder.period = period_model_for_builder
            else:
                # This case implies an issue with period processing not caught above,
                # or period was not a Period or valid tuple.
                # period_name and period_tuple will retain their default "Unknown" values.
                error_msg = "Period model for query_builder could not be determined. Period data might be invalid."
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # Set max query size - track this step
            max_query_size_cmd = "SET max_query_size = 100000000;"
            query_steps.append(max_query_size_cmd)
            if not self.connection.execute_command(max_query_size_cmd):
                error_msg = "Failed to set max_query_size, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # Set max AST size - track this step
            max_ast_size_cmd = "SET max_ast_elements = 10000000;"
            query_steps.append(max_ast_size_cmd)
            if not self.connection.execute_command(max_ast_size_cmd):
                error_msg = "Failed to set max_ast_elements, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # Prepare all queries first
            # Create tables with main data and buyers uplift factor
            pre_axis_query = self.query_builder.query_pre_axis(
                catman=True if kpi_type == KPIType.CATMAN_KPI else False
            )
            query_steps.append(f"-- Pre-axis query:\n{pre_axis_query}")

            # Generate custom query ID for pre-axis
            pre_axis_query_id = f"{query_id_prefix}pre_axis_{uuid.uuid4().hex[:8]}"
            query_ids.append(pre_axis_query_id)

            # Create axis query
            axis_query = self.query_builder.query_axis()
            query_steps.append(f"-- Axis query:\n{axis_query}")

            # Generate custom query ID for axis
            axis_query_id = f"{query_id_prefix}axis_{uuid.uuid4().hex[:8]}"
            query_ids.append(axis_query_id)

            # Create buyers query
            buyers_query = self.query_builder.query_buyers(
                catman=True if kpi_type == KPIType.CATMAN_KPI else False
            )
            query_steps.append(f"-- Buyers query:\n{buyers_query}")

            # Generate custom query ID for buyers
            buyers_query_id = f"{query_id_prefix}buyers_{uuid.uuid4().hex[:8]}"
            query_ids.append(buyers_query_id)

            # Create final query
            final_query = self.query_builder.query_final(
                catman=True if kpi_type == KPIType.CATMAN_KPI else False
            )
            query_steps.append(f"-- Final query:\n{final_query}")

            # Generate custom query ID for final
            final_query_id = f"{query_id_prefix}final_{uuid.uuid4().hex[:8]}"
            query_ids.append(final_query_id)

            # Set optimization - track this step
            optimize_cmd = "SET optimize_aggregation_in_order = 1;"
            query_steps.append(optimize_cmd)
            if not self.connection.execute_command(optimize_cmd):
                error_msg = (
                    "Failed to set optimize_aggregation_in_order, cannot continue"
                )
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # Check if we need to split the fourth_axis for chunking
            split_axis = getattr(period_model_for_builder, "split_axis", None)
            threshold = getattr(period_model_for_builder, "threshold", None)

            if split_axis == "fourth_axis" and threshold:
                self.logger.info(
                    f"Processing fourth_axis with chunking, threshold: {threshold}"
                )

                # Build pre_axis, axis, buyers_final, Buyers_Uplift_Factor temporary tables once
                # 1. Create 'pre_axis' temporary table
                pre_axis_table = self._create_temp_table(
                    "pre_axis", pre_axis_query, pre_axis_query_id
                )
                if not pre_axis_table:
                    error_msg = "Failed to create pre_axis table, cannot continue"
                    self.logger.error(error_msg)
                    raise QueryError(error_msg)

                # 2. Create 'axis' temporary table (which depends on 'pre_axis')
                axis_table = self._create_temp_table("axis", axis_query, axis_query_id)
                if not axis_table:
                    error_msg = "Failed to create axis table, cannot continue"
                    self.logger.error(error_msg)
                    raise QueryError(error_msg)

                # 3. Drop 'pre_axis' temporary table after 'axis' is created but before 'buyers_final' is created
                self._drop_temp_table("pre_axis")

                # 4. Create 'buyers_final' temporary table (which depends on 'axis')
                buyers_table = self._create_temp_table(
                    "buyers_final", buyers_query, buyers_query_id
                )
                if not buyers_table:
                    error_msg = "Failed to create buyers_final table, cannot continue"
                    self.logger.error(error_msg)
                    raise QueryError(error_msg)

                # 5. Drop axis table as it's no longer needed
                self._drop_temp_table("axis")

                # 6. Create 'Buyers_Uplift_Factor' (BUF) table if needed
                buf_table = None
                if "BUF" in self.query_builder.required_facts:
                    buf_query = self.query_builder.query_buf(
                        catman=True if kpi_type == KPIType.CATMAN_KPI else False
                    )
                    query_steps.append(f"-- BUF query:\n{buf_query}")

                    # Generate custom query ID for BUF
                    buf_query_id = f"{query_id_prefix}buf_{uuid.uuid4().hex[:8]}"
                    query_ids.append(buf_query_id)

                    buf_table = self._create_temp_table(
                        "Buyers_Uplift_Factor", buf_query, buf_query_id
                    )
                    if not buf_table:
                        error_msg = "Failed to create Buyers_Uplift_Factor table, cannot continue"
                        self.logger.error(error_msg)
                        raise QueryError(error_msg)

                # Calculate DDL index chunks for "fourth_axis" based on threshold
                fourth_axis_data = self.query_builder.axes["fourth_axis"]
                if fourth_axis_data.ddl and "queries" in fourth_axis_data.ddl:
                    ddl_queries = fourth_axis_data.ddl["queries"]
                    total_ddls = len(ddl_queries)

                    # Calculate number of chunks
                    num_chunks = math.ceil(total_ddls / threshold)
                    self.logger.info(
                        f"Splitting {total_ddls} DDL queries into {num_chunks} chunks of max {threshold} each"
                    )

                    # Store original fourth_axis data
                    original_fourth_axis = copy.deepcopy(fourth_axis_data)

                    # Initialize result_id for chunks
                    chunk_result_id = None

                    # Process each chunk
                    for chunk_idx in range(num_chunks):
                        start_idx = chunk_idx * threshold
                        end_idx = min(start_idx + threshold, total_ddls)
                        chunk_ddls = ddl_queries[start_idx:end_idx]

                        self.logger.info(
                            f"Processing chunk {chunk_idx + 1}/{num_chunks}: DDLs {start_idx} to {end_idx - 1}"
                        )

                        # Create a deep copy of the original fourth_axis AxisData
                        fourth_axis_copy = copy.deepcopy(original_fourth_axis)

                        # Replace its ddl["queries"] with the current chunk's DDLs
                        fourth_axis_copy.ddl["queries"] = chunk_ddls

                        # Temporarily set the sliced copy in query_builder
                        self.query_builder.axes["fourth_axis"] = fourth_axis_copy

                        # Generate final_query_chunk using the sliced fourth_axis
                        final_query_chunk = self.query_builder.query_final(
                            catman=True if kpi_type == KPIType.CATMAN_KPI else False
                        )

                        # Generate custom query ID for this chunk's final query
                        final_chunk_query_id = f"{query_id_prefix}final_chunk_{chunk_idx}_{uuid.uuid4().hex[:8]}"
                        query_ids.append(final_chunk_query_id)

                        # Create a temporary table for this chunk's final data
                        final_temp_chunk_table = f"final_temp_chunk_{chunk_idx}"
                        chunk_table = self._create_temp_table(
                            final_temp_chunk_table,
                            final_query_chunk,
                            final_chunk_query_id,
                        )
                        if not chunk_table:
                            error_msg = f"Failed to create {final_temp_chunk_table} table, cannot continue"
                            self.logger.error(error_msg)
                            raise QueryError(error_msg)

                        # Generate KPI query for this chunk (which selects from final_temp_chunk_data)
                        kpi_query_chunk = self.query_builder.query_kpi(
                            labels=True,
                            position_numbers=True,
                            catman=True if kpi_type == KPIType.CATMAN_KPI else False,
                        )

                        # Generate custom query ID for this chunk's KPI query
                        kpi_chunk_query_id = f"{query_id_prefix}kpi_chunk_{chunk_idx}_{uuid.uuid4().hex[:8]}"
                        query_ids.append(kpi_chunk_query_id)

                        # Calculate job duration so far
                        job_duration_ms = (time.time() - job_start_time) * 1000

                        # Store results for this chunk using consistent combined_result_id
                        chunk_settings = {"query_id": kpi_chunk_query_id}

                        # Try to convert job_id to int for progress message
                        try:
                            job_id_int = int(job_id)
                            set_job_progress_message(
                                job_id_int,
                                f"Storing results for period {period_name}, chunk {chunk_idx + 1}/{num_chunks}",
                            )
                        except (ValueError, TypeError):
                            # Log but continue if job_id is not a valid integer
                            self.logger.warning(
                                f"Could not convert job_id '{job_id}' to integer for progress message"
                            )

                        # Use store_result_direct to append results to the target table
                        chunk_result_id = self.result_store.store_result_direct(
                            query_text=kpi_query_chunk,
                            job_id=job_id,
                            analysis_name=analysis_name,
                            period=period_tuple,
                            kpi_type=kpi_type,
                            id_panel=id_panel,
                            axes=self.query_builder.axes,
                            filters=self.query_builder.filters,
                            combined_result_id=combined_result_id,  # Use consistent ID for all chunks
                            query_steps=f"-- Chunk {chunk_idx + 1}/{num_chunks} Final query:\n{final_query_chunk}\n\n-- Chunk {chunk_idx + 1}/{num_chunks} KPI query:\n{kpi_query_chunk}",
                            query_ids=[final_chunk_query_id, kpi_chunk_query_id],
                            settings=chunk_settings,
                            username=username,
                            su_fact_name=su_fact_name,
                            job_duration=job_duration_ms,
                        )

                        # Drop the temporary chunk table
                        self._drop_temp_table(final_temp_chunk_table)

                        self.logger.info(
                            f"Chunk {chunk_idx + 1}/{num_chunks} processed with result_id: {chunk_result_id}"
                        )

                    # Restore the original fourth_axis AxisData
                    self.query_builder.axes["fourth_axis"] = original_fourth_axis

                    # Clean up the main temporary tables
                    if buf_table:
                        self._drop_temp_table("Buyers_Uplift_Factor")
                    self._drop_temp_table("buyers_final")

                    # Return the last chunk's result_id (all chunks use the same combined_result_id)
                    self.logger.info(f"All {num_chunks} chunks processed successfully")
                    return chunk_result_id, None
                else:
                    self.logger.warning(
                        "fourth_axis has no DDL queries, falling back to normal processing"
                    )
                    # Fall through to normal processing

            # Normal processing (no splitting or split_axis is not "fourth_axis")
            # 1. Create 'pre_axis' temporary table
            pre_axis_table = self._create_temp_table(
                "pre_axis", pre_axis_query, pre_axis_query_id
            )
            if not pre_axis_table:
                error_msg = "Failed to create pre_axis table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 2. Create 'axis' temporary table (which depends on 'pre_axis')
            axis_table = self._create_temp_table("axis", axis_query, axis_query_id)
            if not axis_table:
                error_msg = "Failed to create axis table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 3. Drop 'pre_axis' temporary table after 'axis' is created but before 'buyers_final' is created
            self._drop_temp_table("pre_axis")

            # 4. Create 'buyers_final' temporary table (which depends on 'axis')
            buyers_table = self._create_temp_table(
                "buyers_final", buyers_query, buyers_query_id
            )
            if not buyers_table:
                error_msg = "Failed to create buyers_final table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 5. Drop axis table as it's no longer needed
            self._drop_temp_table("axis")

            # 6. Create 'Buyers_Uplift_Factor' (BUF) table if needed
            buf_table = None
            if "BUF" in self.query_builder.required_facts:
                buf_query = self.query_builder.query_buf(
                    catman=True if kpi_type == KPIType.CATMAN_KPI else False
                )
                query_steps.append(f"-- BUF query:\n{buf_query}")

                # Generate custom query ID for BUF
                buf_query_id = f"{query_id_prefix}buf_{uuid.uuid4().hex[:8]}"
                query_ids.append(buf_query_id)

                buf_table = self._create_temp_table(
                    "Buyers_Uplift_Factor", buf_query, buf_query_id
                )
                if not buf_table:
                    error_msg = (
                        "Failed to create Buyers_Uplift_Factor table, cannot continue"
                    )
                    self.logger.error(error_msg)
                    raise QueryError(error_msg)

            # 7. Create final temporary table (which depends on 'buyers_final' and 'Buyers_Uplift_Factor' if applicable)
            final_table = self._create_temp_table("final", final_query, final_query_id)
            if not final_table:
                error_msg = "Failed to create final table, cannot continue"
                self.logger.error(error_msg)
                raise QueryError(error_msg)

            # 8. Drop BUF table if it was created and buyers_final
            if buf_table:
                self._drop_temp_table("Buyers_Uplift_Factor")
            self._drop_temp_table("buyers_final")

            # 9. Execute the KPI query while all required tables are still available
            kpi_query = self.query_builder.query_kpi(
                labels=True,
                position_numbers=True,
                catman=True if kpi_type == KPIType.CATMAN_KPI else False,
            )
            query_steps.append(f"-- KPI query:\n{kpi_query}")

            # Generate custom query ID for KPI query
            kpi_query_id = f"{query_id_prefix}kpi_{uuid.uuid4().hex[:8]}"
            query_ids.append(kpi_query_id)

            # Join all query steps into a single string
            all_query_steps = "\n\n".join(query_steps)

            # Execute the KPI query with custom query_id
            # This will be stored in the result table
            settings = {"query_id": kpi_query_id}

            # Calculate job duration so far
            job_duration_ms = (time.time() - job_start_time) * 1000
            self.logger.info(f"Job duration so far: {job_duration_ms:.2f} ms")

            # Store the result directly in ClickHouse with custom query_id
            # Try to convert job_id to int for progress message
            try:
                job_id_int = int(job_id)
                set_job_progress_message(
                    job_id_int, f"Storing results for period {period_name}"
                )
            except (ValueError, TypeError):
                # Log but continue if job_id is not a valid integer
                self.logger.warning(
                    f"Could not convert job_id '{job_id}' to integer for progress message"
                )

            result_id = self.result_store.store_result_direct(
                query_text=kpi_query,
                job_id=job_id,
                analysis_name=analysis_name,
                period=period_tuple,
                kpi_type=kpi_type,
                id_panel=id_panel,
                axes=self.query_builder.axes,
                filters=self.query_builder.filters,
                combined_result_id=combined_result_id,
                query_steps=all_query_steps,
                query_ids=query_ids,
                settings=settings,
                username=username,
                su_fact_name=su_fact_name,
                job_duration=job_duration_ms,
            )

            # Drop all remaining temporary tables in reverse order of creation
            self._drop_temp_table("final")

            self.logger.info(f"Result stored directly with ID: {result_id}")
            return result_id, None

        except Exception as e:
            # Calculate job duration so far
            job_duration_ms = (time.time() - job_start_time) * 1000

            # Create a simplified error message
            current_period_name_for_error = (
                period_name if period_name else "Unknown Period"
            )
            error_msg = f"Query failed for period {current_period_name_for_error}: {e}"
            self.logger.error(error_msg)

            # Extract error code if available
            error_code = getattr(e, "error_code", None)
            error_message = str(e)

            if not error_code:  # If not directly available, try regex
                # Try to extract error code from the message using different patterns
                import re  # Import locally to avoid unused global import warning if not needed

                # Pattern 1: "error code 62"
                match = re.search(r"error code (\d+)", error_message.lower())
                if match:
                    error_code = int(match.group(1))
                else:
                    # Pattern 2: "Code: 62."
                    match = re.search(r"Code: (\d+)[.\s]", error_message)
                    if match:
                        error_code = int(match.group(1))

            # Log the extracted error code
            if error_code:
                self.logger.info(f"Extracted ClickHouse error code: {error_code}")

            # Create error info for storage
            error_info = {
                "message": error_msg,
                "error_code": error_code,
                "period": period_name,  # period_name is guaranteed
            }

            # Store the error directly without using store_error function
            try:
                # Store the error in ClickHouse
                result_id = self.result_store.store_result_direct(
                    query_text="SELECT 'Error' AS error",  # Dummy query
                    job_id=job_id,
                    period=period_tuple,
                    query_steps="\n\n".join(query_steps) if query_steps else "",
                    query_ids=query_ids,
                    job_duration=job_duration_ms,
                    error_info=error_info,
                    analysis_name=analysis_name,
                    kpi_type=kpi_type,
                    id_panel=id_panel,
                    axes=self.query_builder.axes,
                    filters=self.query_builder.filters,
                    combined_result_id=combined_result_id,
                    username=username,
                    su_fact_name=su_fact_name,
                )
                self.logger.info(
                    f"Error information stored in ClickHouse for job {job_id} with result_id {result_id}"
                )
                # Return the result_id even for error cases to ensure it's tracked
                return result_id, error_info
            except Exception as store_error:
                self.logger.error(f"Failed to store error information: {store_error}")
                # Try one more time with minimal information
                try:
                    # Create a simplified error record with minimal information
                    minimal_error_info = {
                        "message": f"Error in job {job_id}: {error_message}",
                        "error_code": error_code,
                        "period": period_name,
                        "storage_error": str(store_error),
                    }
                    result_id = self.result_store.store_result_direct(
                        query_text="SELECT 'Error' AS error",  # Dummy query
                        job_id=job_id,
                        period=period_tuple,
                        query_steps="",  # Skip query steps to reduce complexity
                        query_ids=[],
                        job_duration=job_duration_ms,
                        error_info=minimal_error_info,
                        analysis_name=analysis_name,
                        kpi_type=kpi_type,
                        id_panel=id_panel,
                        axes={},  # Empty axes to reduce complexity
                        filters={},  # Empty filters to reduce complexity
                        combined_result_id=combined_result_id,
                        username=username,
                        su_fact_name=su_fact_name,
                    )
                    self.logger.info(
                        f"Minimal error information stored in ClickHouse for job {job_id} with result_id {result_id}"
                    )
                    return result_id, minimal_error_info
                except Exception as minimal_store_error:
                    self.logger.error(
                        f"Failed to store even minimal error information: {minimal_store_error}"
                    )

            # Return None for the result_id and the error_info to indicate failure
            return None, error_info

        finally:
            # Calculate total job duration
            total_job_duration_ms = (time.time() - job_start_time) * 1000
            self.logger.info(f"Total job duration: {total_job_duration_ms:.2f} ms")

            # Clean up any remaining temporary tables that weren't explicitly dropped
            if self.temp_tables:
                self.logger.info(
                    f"Cleaning up {len(self.temp_tables)} remaining temporary tables"
                )
                self._cleanup_temp_tables(self.temp_tables)
